-- TMS Application Complete Database Setup
-- Copy and paste these commands into Supabase SQL Editor

-- Drop existing tables if they exist
DROP TABLE IF EXISTS pre_cert_med_list_form;
DROP TABLE IF EXISTS med_history_form;
DROP TABLE IF EXISTS phq9_form;
DROP TABLE IF EXISTS bdi_form;
DROP TABLE IF EXISTS patient_sessions;
DROP TABLE IF EXISTS patients;

-- Create patients table (matching Patient Demographic Sheet form order)
CREATE TABLE patients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Personal Information Section
    full_legal_name TEXT NOT NULL,
    date DATE NOT NULL,
    phone TEXT NOT NULL,
    email TEXT NOT NULL,
    address TEXT NOT NULL,
    city_state_zip TEXT NOT NULL,
    age INTEGER NOT NULL,
    date_of_birth DATE NOT NULL,
    ssn TEXT,
    gender TEXT NOT NULL,
    
    -- Military Information Section
    active_duty_service_member TEXT NOT NULL,
    dod_benefit TEXT,
    current_employer TEXT,
    
    -- Spouse Information Section
    spouse_name TEXT,
    spouse_age INTEGER,
    spouse_date_of_birth DATE,
    spouse_ssn TEXT,
    spouse_employer TEXT,
    
    -- Healthcare Information Section
    referring_provider TEXT,
    primary_health_insurance TEXT,
    policy TEXT,
    group_number TEXT,
    
    -- Medical Information Section
    known_medical_conditions TEXT,
    drug_allergies TEXT,
    current_medications TEXT,
    
    -- Emergency Contact Section
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    emergency_contact_relationship TEXT
);

-- Create patient_sessions table
CREATE TABLE patient_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    temporary_id TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    patient_id UUID REFERENCES patients(id),
    is_converted BOOLEAN DEFAULT FALSE
);

-- Create bdi_form table (matching BDI form order)
CREATE TABLE bdi_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    total_score INTEGER NOT NULL,
    -- Individual question responses in order
    sadness_response TEXT NOT NULL,
    pessimism_response TEXT NOT NULL,
    past_failure_response TEXT NOT NULL,
    loss_of_pleasure_response TEXT NOT NULL,
    guilty_feelings_response TEXT NOT NULL,
    punishment_feelings_response TEXT NOT NULL,
    self_dislike_response TEXT NOT NULL,
    self_criticalness_response TEXT NOT NULL,
    suicidal_thoughts_response TEXT NOT NULL,
    crying_response TEXT NOT NULL,
    agitation_response TEXT NOT NULL,
    loss_of_interest_response TEXT NOT NULL,
    indecisiveness_response TEXT NOT NULL,
    worthlessness_response TEXT NOT NULL,
    loss_of_energy_response TEXT NOT NULL,
    sleep_changes_response TEXT NOT NULL,
    irritability_response TEXT NOT NULL,
    appetite_changes_response TEXT NOT NULL,
    concentration_difficulty_response TEXT NOT NULL,
    tiredness_response TEXT NOT NULL,
    loss_of_interest_sex_response TEXT NOT NULL,
    assessment_date DATE NOT NULL
);

-- Create phq9_form table (matching PHQ-9 form order)
CREATE TABLE phq9_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    total_score INTEGER NOT NULL,
    -- Individual question responses in order
    interest_pleasure_response TEXT NOT NULL,
    feeling_down_response TEXT NOT NULL,
    sleep_problems_response TEXT NOT NULL,
    tired_energy_response TEXT NOT NULL,
    appetite_problems_response TEXT NOT NULL,
    self_worth_response TEXT NOT NULL,
    concentration_problems_response TEXT NOT NULL,
    movement_problems_response TEXT NOT NULL,
    suicidal_thoughts_response TEXT NOT NULL,
    assessment_date DATE NOT NULL
);

-- Create med_history_form table (matching Medical History form order)
CREATE TABLE med_history_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Medical Conditions Section
    conditions JSONB NOT NULL,
    other_conditions TEXT,
    
    -- Allergies Section
    allergies TEXT[] NOT NULL,
    other_allergies TEXT,
    
    -- Current Medications Section
    current_medications JSONB NOT NULL,
    other_medications TEXT,
    
    -- Past Medications Section
    past_medications JSONB,
    
    -- Family History Section
    family_history JSONB NOT NULL,
    other_family_history TEXT,
    
    -- Psychiatric History Section
    previous_psychiatrist TEXT,
    psychiatric_hospitalizations TEXT,
    
    assessment_date DATE NOT NULL
);

-- Create pre_cert_med_list_form table (matching Pre-Cert Medication List form order)
CREATE TABLE pre_cert_med_list_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patients(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Personal Information Section
    name TEXT NOT NULL,
    date_of_birth DATE NOT NULL,
    
    -- Medication Details Section
    medications JSONB NOT NULL,
    other_medications TEXT,
    medication_details JSONB,
    
    assessment_date DATE NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_patient_sessions_temp_id ON patient_sessions(temporary_id);
CREATE INDEX idx_patient_sessions_patient_id ON patient_sessions(patient_id);
CREATE INDEX idx_bdi_form_patient_id ON bdi_form(patient_id);
CREATE INDEX idx_bdi_form_session_id ON bdi_form(patient_session_id);
CREATE INDEX idx_phq9_form_patient_id ON phq9_form(patient_id);
CREATE INDEX idx_phq9_form_session_id ON phq9_form(patient_session_id);
CREATE INDEX idx_med_history_form_patient_id ON med_history_form(patient_id);
CREATE INDEX idx_med_history_form_session_id ON med_history_form(patient_session_id);
CREATE INDEX idx_pre_cert_med_list_form_patient_id ON pre_cert_med_list_form(patient_id);
CREATE INDEX idx_pre_cert_med_list_form_session_id ON pre_cert_med_list_form(patient_session_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_patients_updated_at 
    BEFORE UPDATE ON patients 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bdi_form_updated_at 
    BEFORE UPDATE ON bdi_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_phq9_form_updated_at 
    BEFORE UPDATE ON phq9_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_med_history_form_updated_at 
    BEFORE UPDATE ON med_history_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pre_cert_med_list_form_updated_at 
    BEFORE UPDATE ON pre_cert_med_list_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
