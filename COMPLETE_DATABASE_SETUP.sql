-- TMS Application Complete Database Setup
-- Copy and paste these commands into Supabase SQL Editor

-- Drop existing tables if they exist
DROP TABLE IF EXISTS patient_medications;
DROP TABLE IF EXISTS medications;
DROP TABLE IF EXISTS pre_cert_med_list_form;
DROP TABLE IF EXISTS med_history_form;
DROP TABLE IF EXISTS phq9_form;
DROP TABLE IF EXISTS bdi_form;
DROP TABLE IF EXISTS patient_sessions;
DROP TABLE IF EXISTS patient_intake_form;

-- Create patient_intake_form table (matching Patient Demographic Sheet form order)
CREATE TABLE patient_intake_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Personal Information Section
    full_legal_name TEXT NOT NULL,
    date DATE NOT NULL,
    phone TEXT NOT NULL,
    email TEXT NOT NULL,
    address TEXT NOT NULL,
    city_state_zip TEXT NOT NULL,
    age INTEGER NOT NULL,
    date_of_birth DATE NOT NULL,
    ssn TEXT,
    gender TEXT NOT NULL,
    
    -- Military Information Section
    active_duty_service_member TEXT NOT NULL,
    dod_benefit TEXT,
    current_employer TEXT,
    
    -- Spouse Information Section
    spouse_name TEXT,
    spouse_age INTEGER,
    spouse_date_of_birth DATE,
    spouse_ssn TEXT,
    spouse_employer TEXT,
    
    -- Healthcare Information Section
    referring_provider TEXT,
    primary_health_insurance TEXT,
    policy TEXT,
    group_number TEXT,
    
    -- Medical Information Section
    known_medical_conditions TEXT,
    drug_allergies TEXT,
    current_medications TEXT,
    
    -- Emergency Contact Section
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    emergency_contact_relationship TEXT
);

-- Create patient_sessions table
CREATE TABLE patient_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    temporary_id TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    patient_id UUID REFERENCES patient_intake_form(id),
    is_converted BOOLEAN DEFAULT FALSE
);

-- Create bdi_form table (matching BDI form order)
CREATE TABLE bdi_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patient_intake_form(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    total_score INTEGER NOT NULL,
    -- Individual question responses in order
    sadness_response TEXT NOT NULL,
    pessimism_response TEXT NOT NULL,
    past_failure_response TEXT NOT NULL,
    loss_of_pleasure_response TEXT NOT NULL,
    guilty_feelings_response TEXT NOT NULL,
    punishment_feelings_response TEXT NOT NULL,
    self_dislike_response TEXT NOT NULL,
    self_criticalness_response TEXT NOT NULL,
    suicidal_thoughts_response TEXT NOT NULL,
    crying_response TEXT NOT NULL,
    agitation_response TEXT NOT NULL,
    loss_of_interest_response TEXT NOT NULL,
    indecisiveness_response TEXT NOT NULL,
    worthlessness_response TEXT NOT NULL,
    loss_of_energy_response TEXT NOT NULL,
    sleep_changes_response TEXT NOT NULL,
    irritability_response TEXT NOT NULL,
    appetite_changes_response TEXT NOT NULL,
    concentration_difficulty_response TEXT NOT NULL,
    tiredness_response TEXT NOT NULL,
    loss_of_interest_sex_response TEXT NOT NULL,
    assessment_date DATE NOT NULL
);

-- Create phq9_form table (matching PHQ-9 form order)
CREATE TABLE phq9_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patient_intake_form(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    total_score INTEGER NOT NULL,
    -- Individual question responses in order
    interest_pleasure_response TEXT NOT NULL,
    feeling_down_response TEXT NOT NULL,
    sleep_problems_response TEXT NOT NULL,
    tired_energy_response TEXT NOT NULL,
    appetite_problems_response TEXT NOT NULL,
    self_worth_response TEXT NOT NULL,
    concentration_problems_response TEXT NOT NULL,
    movement_problems_response TEXT NOT NULL,
    suicidal_thoughts_response TEXT NOT NULL,
    assessment_date DATE NOT NULL
);

-- Create med_history_form table (matching Medical History form order)
CREATE TABLE med_history_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patient_intake_form(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,

    -- Medical Conditions Section (individual columns for each condition)
    asthma BOOLEAN DEFAULT FALSE,
    headache BOOLEAN DEFAULT FALSE,
    heart_disease BOOLEAN DEFAULT FALSE,
    appetite_problems BOOLEAN DEFAULT FALSE,
    weight_loss_gain BOOLEAN DEFAULT FALSE,
    sleep_difficulty BOOLEAN DEFAULT FALSE,
    anxiety BOOLEAN DEFAULT FALSE,
    stomach_trouble BOOLEAN DEFAULT FALSE,
    constipation BOOLEAN DEFAULT FALSE,
    glaucoma BOOLEAN DEFAULT FALSE,
    aids_hiv BOOLEAN DEFAULT FALSE,
    hepatitis BOOLEAN DEFAULT FALSE,
    thyroid_disease BOOLEAN DEFAULT FALSE,
    syphilis BOOLEAN DEFAULT FALSE,
    seizures BOOLEAN DEFAULT FALSE,
    gonorrhea BOOLEAN DEFAULT FALSE,
    tb BOOLEAN DEFAULT FALSE,
    high_blood_pressure BOOLEAN DEFAULT FALSE,
    diabetes BOOLEAN DEFAULT FALSE,
    drinking_problems BOOLEAN DEFAULT FALSE,
    substance_abuse BOOLEAN DEFAULT FALSE,
    fatigue BOOLEAN DEFAULT FALSE,
    loss_of_concentration BOOLEAN DEFAULT FALSE,
    recurrent_thoughts BOOLEAN DEFAULT FALSE,
    sexual_problems BOOLEAN DEFAULT FALSE,

    -- Suicidal History Section
    suicidal_thoughts TEXT,
    attempts TEXT,
    suicidal_explanation TEXT,

    -- Psychiatric History Section
    previous_psychiatrist TEXT,
    psychiatric_hospitalizations TEXT,

    -- Other Section
    legal_charges TEXT,
    legal_explanation TEXT,

    -- Allergies Section
    allergies TEXT,

    -- Authorization Section
    signature TEXT,

    assessment_date DATE DEFAULT CURRENT_DATE
);

-- Create medications reference table
CREATE TABLE medications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    generic_name TEXT NOT NULL,
    category TEXT NOT NULL, -- SSRI, SNRI, TRICYCLIC, MAOI, ATYPICAL, AUGMENTING_AGENT
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create patient_medications table
CREATE TABLE patient_medications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patient_intake_form(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    medication_id UUID REFERENCES medications(id),
    is_taken BOOLEAN DEFAULT FALSE,
    dose TEXT,
    start_date DATE,
    end_date DATE,
    reason_discontinuing TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    assessment_date DATE DEFAULT CURRENT_DATE
);

-- Create pre_cert_med_list_form table (simplified)
CREATE TABLE pre_cert_med_list_form (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    patient_id UUID REFERENCES patient_intake_form(id),
    patient_session_id UUID REFERENCES patient_sessions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    assessment_date DATE DEFAULT CURRENT_DATE
);

-- Create indexes for better performance
CREATE INDEX idx_patient_sessions_temp_id ON patient_sessions(temporary_id);
CREATE INDEX idx_patient_sessions_patient_id ON patient_sessions(patient_id);
CREATE INDEX idx_patient_intake_form_email ON patient_intake_form(email);
CREATE INDEX idx_patient_intake_form_phone ON patient_intake_form(phone);
CREATE INDEX idx_bdi_form_patient_id ON bdi_form(patient_id);
CREATE INDEX idx_bdi_form_session_id ON bdi_form(patient_session_id);
CREATE INDEX idx_bdi_form_assessment_date ON bdi_form(assessment_date);
CREATE INDEX idx_phq9_form_patient_id ON phq9_form(patient_id);
CREATE INDEX idx_phq9_form_session_id ON phq9_form(patient_session_id);
CREATE INDEX idx_phq9_form_assessment_date ON phq9_form(assessment_date);
CREATE INDEX idx_med_history_form_patient_id ON med_history_form(patient_id);
CREATE INDEX idx_med_history_form_session_id ON med_history_form(patient_session_id);
CREATE INDEX idx_med_history_form_assessment_date ON med_history_form(assessment_date);
CREATE INDEX idx_patient_medications_patient_id ON patient_medications(patient_id);
CREATE INDEX idx_patient_medications_session_id ON patient_medications(patient_session_id);
CREATE INDEX idx_patient_medications_medication_id ON patient_medications(medication_id);
CREATE INDEX idx_patient_medications_assessment_date ON patient_medications(assessment_date);
CREATE INDEX idx_pre_cert_med_list_form_patient_id ON pre_cert_med_list_form(patient_id);
CREATE INDEX idx_pre_cert_med_list_form_session_id ON pre_cert_med_list_form(patient_session_id);
CREATE INDEX idx_pre_cert_med_list_form_assessment_date ON pre_cert_med_list_form(assessment_date);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_patient_intake_form_updated_at
    BEFORE UPDATE ON patient_intake_form
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bdi_form_updated_at 
    BEFORE UPDATE ON bdi_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_phq9_form_updated_at 
    BEFORE UPDATE ON phq9_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_med_history_form_updated_at 
    BEFORE UPDATE ON med_history_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_patient_medications_updated_at 
    BEFORE UPDATE ON patient_medications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pre_cert_med_list_form_updated_at 
    BEFORE UPDATE ON pre_cert_med_list_form 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial medications data
INSERT INTO medications (name, generic_name, category) VALUES
-- SSRI Medications
('Zoloft', 'Sertraline', 'SSRI'),
('Prozac', 'Fluoxetine', 'SSRI'),
('Celexa', 'Citalopram', 'SSRI'),
('Luvox', 'Fluvoxamine', 'SSRI'),
('Paxil', 'Paroxetine', 'SSRI'),
('Paxil CR', 'Paroxetine CR', 'SSRI'),
('Lexapro', 'Escitalopram', 'SSRI'),
('Viibryd', 'Vilazodone', 'SSRI'),
('Brintellix', 'Vortioxetine', 'SSRI'),

-- SNRI Medications
('Effexor', 'Venlafaxine', 'SNRI'),
('Cymbalta', 'Duloxetine', 'SNRI'),
('Pristiq', 'Desvenlafaxine', 'SNRI'),
('Fetzima', 'Levomilnacipran', 'SNRI'),
('Savella', 'Milnacipran', 'SNRI'),

-- TRICYCLIC Medications
('Elavil', 'Amitriptyline', 'TRICYCLIC'),
('Tofranil', 'Imipramine', 'TRICYCLIC'),
('Norpramin', 'Desipramine', 'TRICYCLIC'),
('Surmontil', 'Trimipramine', 'TRICYCLIC'),
('Anafranil', 'Clomipramine', 'TRICYCLIC'),
('Ludiomil', 'Maprotiline', 'TRICYCLIC'),
('Sinequan', 'Doxepin', 'TRICYCLIC'),
('Merital', 'Nomifensine', 'TRICYCLIC'),
('Pamelor', 'Nortriptyline', 'TRICYCLIC'),
('Vivactil', 'Protriptyline', 'TRICYCLIC'),
('Asendin', 'Amoxapine', 'TRICYCLIC'),

-- MAOI Medications
('Nardil', 'Phenelzine', 'MAOI'),
('Emsam', 'Selegiline', 'MAOI'),
('Emsam Patch', 'Selegiline Patch', 'MAOI'),
('Marplan', 'Isocarboxazid', 'MAOI'),
('Parnate', 'Tranylcypromine', 'MAOI'),

-- ATYPICAL Medications
('Wellbutrin', 'Bupropion', 'ATYPICAL'),
('Serzone', 'Nefazodone', 'ATYPICAL'),
('Desyrel', 'Trazodone', 'ATYPICAL'),
('Remeron', 'Mirtazapine', 'ATYPICAL'),

-- AUGMENTING AGENT Medications
('Abilify', 'Aripiprazole', 'AUGMENTING_AGENT'),
('Geodon', 'Ziprasidone', 'AUGMENTING_AGENT'),
('Risperdal', 'Risperidone', 'AUGMENTING_AGENT'),
('Seroquel', 'Quetiapine', 'AUGMENTING_AGENT'),
('Zyprexa', 'Olanzapine', 'AUGMENTING_AGENT'),
('Saphris', 'Asenapine', 'AUGMENTING_AGENT'),
('Vraylar', 'Cariprazine', 'AUGMENTING_AGENT'),
('Latuda', 'Lurasidone', 'AUGMENTING_AGENT'),
('Clozaril', 'Clozapine', 'AUGMENTING_AGENT'),
('Invega', 'Paliperidone', 'AUGMENTING_AGENT'),
('Rexulti', 'Brexpiprazole', 'AUGMENTING_AGENT'),
('Eskalith', 'Lithium', 'AUGMENTING_AGENT'),
('Neurontin', 'Gabapentin', 'AUGMENTING_AGENT'),
('Lamictal', 'Lamotrigine', 'AUGMENTING_AGENT'),
('Topamax', 'Topiramate', 'AUGMENTING_AGENT');
