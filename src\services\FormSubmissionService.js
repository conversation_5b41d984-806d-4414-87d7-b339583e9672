import { supabase } from '../lib/supabase';
import PatientSessionService from './PatientSessionService';
import OfflineStorageService from './OfflineStorageService';
import ValidationService from './ValidationService';
import { Alert } from 'react-native';

class FormSubmissionService {
  
  /**
   * Get session UUID from temporary ID
   */
  static async getSessionUuid(sessionId) {
    try {
      // First try to get the session by temporary_id
      const { data: session, error } = await supabase
      .from('patient_sessions')
      .select('id')
      .eq('temporary_id', sessionId)
      .single();

    if (error) {
        console.error('Error getting session UUID:', error);
        throw new Error('Failed to get session UUID');
      }

      if (!session || !session.id) {
        throw new Error('Session not found');
      }

      return session.id;
    } catch (error) {
      console.error('Error in getSessionUuid:', error);
      throw error;
    }
  }

  /**
   * Calculate BDI severity level based on total score
   */
  static calculateBDISeverity(totalScore) {
    if (totalScore <= 13) return 'Minimal';
    if (totalScore <= 19) return 'Mild';
    if (totalScore <= 28) return 'Moderate';
    if (totalScore <= 63) return 'Severe';
    return 'Extreme';
  }

  /**
   * Calculate PHQ-9 severity level based on total score
   */
  static calculatePHQ9Severity(totalScore) {
    if (totalScore <= 4) return 'Minimal';
    if (totalScore <= 9) return 'Mild';
    if (totalScore <= 14) return 'Moderate';
    if (totalScore <= 19) return 'Moderately Severe';
    return 'Severe';
  }

  /**
   * Submit BDI assessment
   */
  static async submitBDIAssessment(formData) {
    try {
      const tempSessionId = await PatientSessionService.getCurrentSessionId();
      const sessionUUID = await this.getSessionUuid(tempSessionId);

      const bdiData = {
        patient_session_id: sessionUUID,
        total_score: formData.totalScore,
        sadness_response: formData.responses[0]?.toString() || '0',
        pessimism_response: formData.responses[1]?.toString() || '0',
        past_failure_response: formData.responses[2]?.toString() || '0',
        loss_of_pleasure_response: formData.responses[3]?.toString() || '0',
        guilty_feelings_response: formData.responses[4]?.toString() || '0',
        punishment_feelings_response: formData.responses[5]?.toString() || '0',
        self_dislike_response: formData.responses[6]?.toString() || '0',
        self_criticalness_response: formData.responses[7]?.toString() || '0',
        suicidal_thoughts_response: formData.responses[8]?.toString() || '0',
        crying_response: formData.responses[9]?.toString() || '0',
        agitation_response: formData.responses[10]?.toString() || '0',
        loss_of_interest_response: formData.responses[11]?.toString() || '0',
        indecisiveness_response: formData.responses[12]?.toString() || '0',
        worthlessness_response: formData.responses[13]?.toString() || '0',
        loss_of_energy_response: formData.responses[14]?.toString() || '0',
        sleep_changes_response: formData.responses[15]?.toString() || '0',
        irritability_response: formData.responses[16]?.toString() || '0',
        appetite_changes_response: formData.responses[17]?.toString() || '0',
        concentration_difficulty_response: formData.responses[18]?.toString() || '0',
        tiredness_response: formData.responses[19]?.toString() || '0',
        loss_of_interest_sex_response: formData.responses[20]?.toString() || '0',
        assessment_date: new Date().toISOString().split('T')[0]
      };

      const { data, error } = await supabase
        .from('bdi_form')
        .insert([bdiData]);

      if (error) {
        console.error('Error submitting BDI assessment:', error);
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error submitting BDI assessment:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit PHQ-9 screening
   */
  static async submitPHQ9Screening(formData) {
    try {
      const tempSessionId = await PatientSessionService.getCurrentSessionId();
      const sessionUUID = await this.getSessionUuid(tempSessionId);

      const phq9Data = {
        patient_session_id: sessionUUID,
        total_score: formData.totalScore,
        interest_pleasure_response: formData.responses[0]?.toString() || '0',
        feeling_down_response: formData.responses[1]?.toString() || '0',
        sleep_problems_response: formData.responses[2]?.toString() || '0',
        tired_energy_response: formData.responses[3]?.toString() || '0',
        appetite_problems_response: formData.responses[4]?.toString() || '0',
        self_worth_response: formData.responses[5]?.toString() || '0',
        concentration_problems_response: formData.responses[6]?.toString() || '0',
        movement_problems_response: formData.responses[7]?.toString() || '0',
        suicidal_thoughts_response: formData.responses[8]?.toString() || '0',
        assessment_date: new Date().toISOString().split('T')[0]
      };

      const { data, error } = await supabase
        .from('phq9_form')
        .insert([phq9Data]);

      if (error) {
        console.error('Error submitting PHQ-9 screening:', error);
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error submitting PHQ-9 screening:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit medical history
   */
  static async submitMedicalHistory(formData) {
    try {
      const tempSessionId = await PatientSessionService.getCurrentSessionId();
      const sessionUUID = await this.getSessionUuid(tempSessionId);

      const medicalHistoryData = {
        patient_session_id: sessionUUID,
        medical_conditions: formData.medicalConditions,
        allergies: formData.allergies,
        current_medications: formData.currentMedications,
        family_history: formData.familyHistory,
        assessment_date: new Date().toISOString().split('T')[0]
      };

      const { data, error } = await supabase
        .from('med_history_form')
        .insert([medicalHistoryData]);

      if (error) {
        console.error('Error submitting medical history:', error);
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error submitting medical history:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit pre-certification medication list
   */
  static async submitPreCertMedList(formData) {
    try {
      // Get the session UUID
      const tempSessionId = await PatientSessionService.getCurrentSessionId();
      const sessionUUID = await this.getSessionUuid(tempSessionId);

      // Create the pre-cert med list form entry with all medication data
      const { data: preCertData, error: preCertError } = await supabase
        .from('pre_cert_med_list_form')
        .insert({
          patient_session_id: sessionUUID,
          assessment_date: new Date().toISOString().split('T')[0],
          medications: formData.medications // Store the entire medications object
        })
        .select()
        .single();

      if (preCertError) {
        console.error('Error submitting pre-cert medication list:', preCertError);
        return { success: false, error: preCertError.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting pre-cert medication list:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit patient demographic data
   */
  static async submitPatientDemographics(formData) {
    try {
      const tempSessionId = await PatientSessionService.getCurrentSessionId();
      const sessionUUID = await this.getSessionUuid(tempSessionId);

      // Split full legal name into first and last name
      const nameParts = formData.fullLegalName.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const patientData = {
        patient_session_id: sessionUUID,
        first_name: firstName,
        last_name: lastName,
        date_of_birth: formData.dob,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        city_state_zip: formData.cityStateZip,
        age: formData.age,
        ssn: formData.ssn,
        gender: formData.gender,
        active_duty_service_member: formData.activeDutyServiceMember,
        dod_benefit: formData.dodBenefit,
        current_employer: formData.currentEmployer,
        spouse_name: formData.spouseName,
        spouse_age: formData.spouseAge,
        spouse_dob: formData.spouseDob,
        spouse_ssn: formData.spouseSsn,
        spouse_employer: formData.spouseEmployer,
        referring_provider: formData.referringProvider,
        primary_health_insurance: formData.primaryHealthInsurance,
        policy: formData.policy,
        group: formData.group,
        known_medical_conditions: formData.knownMedicalConditions,
        drug_allergies: formData.drugAllergies,
        current_medications: formData.currentMedications,
        emergency_contact_name: formData.emergencyContactName,
        emergency_contact_phone: formData.emergencyContactPhone,
        emergency_contact_relationship: formData.emergencyContactRelationship,
        assessment_date: new Date().toISOString().split('T')[0]
      };

      const { data, error } = await supabase
        .from('patient_intake_form')
        .insert([patientData]);

      if (error) {
        console.error('Error submitting patient demographics:', error);
        throw error;
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error submitting patient demographics:', error);
      return { success: false, error: error.message };
    }
  }

  // Submit form with validation and offline support
  static async submitForm(formType, formData, validationRules, submitFunction) {
    try {
      // Validate form data
      const validationResult = ValidationService.validateFormData(formData, validationRules);
      if (!validationResult.isValid) {
        // Show validation errors in a user-friendly way
        const errorMessages = Object.values(validationResult.errors).join('\n');
        Alert.alert(
          'Validation Error',
          errorMessages,
          [{ text: 'OK' }]
        );
        return { success: false, errors: validationResult.errors };
      }

      // Check if online
      const isOnline = await OfflineStorageService.isOnline();
      
      if (!isOnline) {
        // Save form data offline and add to pending submissions
        await OfflineStorageService.saveOfflineFormData(formType, formData);
        await OfflineStorageService.addPendingSubmission(formType, formData);
        
        Alert.alert(
          'Offline Mode',
          'You are currently offline. Your form has been saved and will be submitted when you are back online.',
          [{ text: 'OK' }]
        );
        
        return { success: true, offline: true };
      }

      // Online submission
      try {
        const result = await submitFunction(formData);
        return { success: true, data: result };
      } catch (error) {
        // Handle submission error
        console.error('Form submission error:', error);
        
        // Save failed submission for retry
        await OfflineStorageService.addPendingSubmission(formType, formData);
        
        Alert.alert(
          'Submission Error',
          'There was an error submitting your form. It has been saved and will be retried automatically.',
          [{ text: 'OK' }]
        );
        
        return { success: false, error: error.message };
      }
    } catch (error) {
      console.error('Form submission service error:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.message };
    }
  }

  // Retry pending submissions
  static async retryPendingSubmissions(submitFunction) {
    try {
      const pendingSubmissions = await OfflineStorageService.getPendingSubmissions();
      const isOnline = await OfflineStorageService.isOnline();

      if (!isOnline || pendingSubmissions.length === 0) {
        return;
      }

      for (const submission of pendingSubmissions) {
        try {
          const result = await submitFunction(submission.formData);
          
          // Remove successful submission from pending queue
          await OfflineStorageService.removePendingSubmission(submission.id);
          
          console.log(`Successfully submitted pending form: ${submission.id}`);
        } catch (error) {
          console.error(`Error retrying submission ${submission.id}:`, error);
          
          // Update retry count
          await OfflineStorageService.updateSubmissionRetryCount(submission.id);
          
          // If too many retries, show error to user
          if (submission.retryCount >= 3) {
            Alert.alert(
              'Submission Failed',
              'A form submission has failed multiple times. Please check your connection and try submitting again.',
              [{ text: 'OK' }]
            );
          }
        }
      }
    } catch (error) {
      console.error('Error retrying pending submissions:', error);
    }
  }

  // Clear form data
  static async clearFormData(formType) {
    try {
      await OfflineStorageService.clearOfflineData();
      return { success: true };
    } catch (error) {
      console.error('Error clearing form data:', error);
      return { success: false, error: error.message };
    }
  }
}

export default FormSubmissionService;
