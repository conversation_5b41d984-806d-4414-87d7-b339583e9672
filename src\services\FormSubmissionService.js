import { supabase } from '../lib/supabase';
import PatientSessionService from './PatientSessionService';

class FormSubmissionService {
  
  /**
   * Get session UUID from temporary ID
   */
  static async getSessionUuid(sessionId) {
    try {
      // First try to get the session by temporary_id
      const { data: session, error } = await supabase
        .from('patient_sessions')
        .select('id')
        .eq('temporary_id', sessionId)
        .single();

      if (error) {
        console.error('Error getting session UUID:', error);
        throw new Error('Failed to get session UUID');
      }

      if (!session || !session.id) {
        throw new Error('Session not found');
      }

      return session.id;
    } catch (error) {
      console.error('Error in getSessionUuid:', error);
      throw error;
    }
  }

  /**
   * Calculate BDI severity level based on total score
   */
  static calculateBDISeverity(totalScore) {
    if (totalScore <= 13) return 'Minimal';
    if (totalScore <= 19) return 'Mild';
    if (totalScore <= 28) return 'Moderate';
    if (totalScore <= 63) return 'Severe';
    return 'Extreme';
  }

  /**
   * Calculate PHQ-9 severity level based on total score
   */
  static calculatePHQ9Severity(totalScore) {
    if (totalScore <= 4) return 'Minimal';
    if (totalScore <= 9) return 'Mild';
    if (totalScore <= 14) return 'Moderate';
    if (totalScore <= 19) return 'Moderately Severe';
    return 'Severe';
  }

  /**
   * Submit BDI assessment
   */
  static async submitBDIAssessment(formData) {
    try {
      const sessionId = await PatientSessionService.getCurrentSessionId();
      if (!sessionId) {
        throw new Error('No active session found');
      }

      // Get the actual UUID for the session
      const sessionUuid = await this.getSessionUuid(sessionId);

      const totalScore = formData.totalScore;
      const severityLevel = this.calculateBDISeverity(totalScore);

      const assessmentData = {
        total_score: totalScore,
        responses: formData.responses,
        severity_level: severityLevel,
        assessment_date: new Date().toISOString().split('T')[0]
      };

      // Check if assessment already exists for this session
      const { data: existingAssessment } = await supabase
        .from('bdi_form')
        .select('id')
        .eq('patient_session_id', sessionUuid)
        .single();

      if (existingAssessment) {
        // Update existing assessment
        const { error } = await supabase
          .from('bdi_form')
          .update(assessmentData)
          .eq('id', existingAssessment.id);

        if (error) throw error;
      } else {
        // Create new assessment
        const { error } = await supabase
          .from('bdi_form')
          .insert([{ ...assessmentData, patient_session_id: sessionUuid }]);

        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting BDI assessment:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit PHQ-9 screening
   */
  static async submitPHQ9Screening(formData) {
    try {
      const sessionId = await PatientSessionService.getCurrentSessionId();
      if (!sessionId) {
        throw new Error('No active session found');
      }

      // Get the actual UUID for the session
      const sessionUuid = await this.getSessionUuid(sessionId);
      if (!sessionUuid) {
        throw new Error('Failed to get session UUID');
      }

      const screeningData = {
        total_score: formData.totalScore,
        responses: formData.responses,
        assessment_date: new Date().toISOString().split('T')[0]
      };

      // Check if screening already exists for this session
      const { data: existingScreening } = await supabase
        .from('phq9_form')
        .select('id')
        .eq('patient_session_id', sessionUuid)
        .single();

      if (existingScreening) {
        // Update existing screening
        const { error } = await supabase
          .from('phq9_form')
          .update(screeningData)
          .eq('id', existingScreening.id);

        if (error) throw error;
      } else {
        // Create new screening
        const { error } = await supabase
          .from('phq9_form')
          .insert([{ ...screeningData, patient_session_id: sessionUuid }]);

        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting PHQ-9 screening:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit medical history
   */
  static async submitMedicalHistory(formData) {
    try {
      const sessionId = await PatientSessionService.getCurrentSessionId();
      if (!sessionId) {
        throw new Error('No active session found');
      }

      // Get the actual UUID for the session
      const sessionUuid = await this.getSessionUuid(sessionId);

      const historyData = {
        conditions: formData.conditions || {},
        other_conditions: formData.otherConditions,
        allergies: formData.allergies || [],
        other_allergies: formData.otherAllergies,
        current_medications: formData.currentMedications || {},
        other_medications: formData.otherMedications,
        past_medications: formData.pastMedications || {},
        family_history: formData.familyHistory || {},
        other_family_history: formData.otherFamilyHistory,
        assessment_date: new Date().toISOString().split('T')[0]
      };

      // Check if history already exists for this session
      const { data: existingHistory } = await supabase
        .from('med_history_form')
        .select('id')
        .eq('patient_session_id', sessionUuid)
        .single();

      if (existingHistory) {
        // Update existing history
        const { error } = await supabase
          .from('med_history_form')
          .update(historyData)
          .eq('id', existingHistory.id);

        if (error) throw error;
      } else {
        // Create new history
        const { error } = await supabase
          .from('med_history_form')
          .insert([{ ...historyData, patient_session_id: sessionUuid }]);

        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting medical history:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit pre-certification medication list
   */
  static async submitPreCertMedList(formData) {
    try {
      const sessionId = await PatientSessionService.getCurrentSessionId();
      if (!sessionId) {
        throw new Error('No active session found');
      }

      // Get the actual UUID for the session
      const sessionUuid = await this.getSessionUuid(sessionId);

      const medListData = {
        medications: formData.medications || {},
        other_medications: formData.otherMedications,
        medication_details: formData.medicationDetails || {},
        assessment_date: new Date().toISOString().split('T')[0]
      };

      // Check if med list already exists for this session
      const { data: existingMedList } = await supabase
        .from('pre_cert_med_list_form')
        .select('id')
        .eq('patient_session_id', sessionUuid)
        .single();

      if (existingMedList) {
        // Update existing med list
        const { error } = await supabase
          .from('pre_cert_med_list_form')
          .update(medListData)
          .eq('id', existingMedList.id);

        if (error) throw error;
      } else {
        // Create new med list
        const { error } = await supabase
          .from('pre_cert_med_list_form')
          .insert([{ ...medListData, patient_session_id: sessionUuid }]);

        if (error) throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting pre-cert medication list:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit patient demographic data
   */
  static async submitPatientDemographics(formData) {
    try {
      // Validate required fields
      if (!formData.age || isNaN(parseInt(formData.age))) {
        throw new Error('Age is required and must be a valid number');
      }

      // Parse form data to match database schema
      const patientData = {
        first_name: formData.fullLegalName.split(' ')[0],
        last_name: formData.fullLegalName.split(' ').slice(1).join(' '),
        date_of_birth: formData.dob,
        gender: formData.gender,
        email: formData.email,
        phone: formData.phone,
        address: formData.address,
        city: formData.cityStateZip.split(',')[0]?.trim(),
        state: formData.cityStateZip.split(',')[1]?.split(' ')[1]?.trim(),
        zip_code: formData.cityStateZip.split(',')[1]?.split(' ')[2]?.trim(),
        emergency_contact_name: formData.emergencyContactName,
        emergency_contact_phone: formData.emergencyContactPhone,
        emergency_contact_relationship: formData.emergencyContactRelationship,
        // Additional fields
        age: parseInt(formData.age),
        ssn: formData.ssn || null,
        date: formData.date || new Date().toISOString().split('T')[0],
        city_state_zip: formData.cityStateZip,
        // Spouse Information
        spouse_name: formData.spouseName || null,
        spouse_age: formData.spouseAge ? parseInt(formData.spouseAge) : null,
        spouse_date_of_birth: formData.spouseDob || null,
        spouse_ssn: formData.spouseSsn || null,
        spouse_employer: formData.spouseEmployer || null,
        // Military Information
        active_duty_service_member: formData.activeDutyServiceMember || 'No',
        dod_benefit: formData.dodBenefit || null,
        // Employment Information
        current_employer: formData.currentEmployer || null,
        // Healthcare Information
        referring_provider: formData.referringProvider || null,
        primary_health_insurance: formData.primaryHealthInsurance || null,
        policy_number: formData.policy || null,
        group_number: formData.group || null,
        // Medical Information
        known_medical_conditions: formData.knownMedicalConditions || null,
        drug_allergies: formData.drugAllergies || null,
        current_medications: formData.currentMedications || null
      };

      // Get current session ID
      const sessionId = await PatientSessionService.getCurrentSessionId();
      if (!sessionId) {
        throw new Error('No active session found');
      }

      // Check if patient record already exists for this session
      const { data: existingSession } = await supabase
        .from('patient_sessions')
        .select('patient_id')
        .eq('temporary_id', sessionId)
        .single();

      if (existingSession?.patient_id) {
        // Update existing patient record
        const { error } = await supabase
          .from('patients')
          .update(patientData)
          .eq('id', existingSession.patient_id);

        if (error) throw error;
      } else {
        // Create new patient record
        const { data: patient, error } = await supabase
          .from('patients')
          .insert([patientData])
          .select()
          .single();

        if (error) throw error;

        // Update session with patient ID
        const { error: sessionError } = await supabase
          .from('patient_sessions')
          .update({ patient_id: patient.id })
          .eq('temporary_id', sessionId);

        if (sessionError) throw sessionError;
      }

      return { success: true };
    } catch (error) {
      console.error('Error submitting patient demographics:', error);
      return { success: false, error: error.message };
    }
  }
}

export default FormSubmissionService;
