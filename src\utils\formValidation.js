// Form Validation Utilities for TMS Application

export class FormValidator {
  
  // Email validation
  static validateEmail(email) {
    if (!email) return { isValid: false, error: 'Email is required' };
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Please enter a valid email address' };
    }
    return { isValid: true, error: '' };
  }

  // Phone validation
  static validatePhone(phone) {
    if (!phone) return { isValid: false, error: 'Phone number is required' };
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    if (cleanPhone.length < 10) {
      return { isValid: false, error: 'Phone number must be at least 10 digits' };
    }
    if (!phoneRegex.test(cleanPhone)) {
      return { isValid: false, error: 'Please enter a valid phone number' };
    }
    return { isValid: true, error: '' };
  }

  // Name validation
  static validateName(name, fieldName = 'Name') {
    if (!name || name.trim() === '') {
      return { isValid: false, error: `${fieldName} is required` };
    }
    if (name.trim().length < 2) {
      return { isValid: false, error: `${fieldName} must be at least 2 characters` };
    }
    if (name.trim().length > 50) {
      return { isValid: false, error: `${fieldName} must be less than 50 characters` };
    }
    const nameRegex = /^[a-zA-Z\s\-\'\.]+$/;
    if (!nameRegex.test(name.trim())) {
      return { isValid: false, error: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
    }
    return { isValid: true, error: '' };
  }

  // Age validation
  static validateAge(age) {
    if (!age) return { isValid: false, error: 'Age is required' };
    const ageNum = parseInt(age);
    if (isNaN(ageNum)) {
      return { isValid: false, error: 'Age must be a number' };
    }
    if (ageNum < 1 || ageNum > 120) {
      return { isValid: false, error: 'Age must be between 1 and 120' };
    }
    return { isValid: true, error: '' };
  }

  // Date validation
  static validateDate(date, fieldName = 'Date') {
    if (!date) return { isValid: false, error: `${fieldName} is required` };
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return { isValid: false, error: `Please enter a valid ${fieldName.toLowerCase()}` };
    }
    return { isValid: true, error: '' };
  }

  // Date of birth validation
  static validateDateOfBirth(dob) {
    if (!dob) return { isValid: false, error: 'Date of birth is required' };
    const dobDate = new Date(dob);
    const today = new Date();
    
    if (isNaN(dobDate.getTime())) {
      return { isValid: false, error: 'Please enter a valid date of birth' };
    }
    
    if (dobDate > today) {
      return { isValid: false, error: 'Date of birth cannot be in the future' };
    }
    
    const age = today.getFullYear() - dobDate.getFullYear();
    if (age > 120) {
      return { isValid: false, error: 'Please enter a valid date of birth' };
    }
    
    return { isValid: true, error: '' };
  }

  // Address validation
  static validateAddress(address) {
    if (!address || address.trim() === '') {
      return { isValid: false, error: 'Address is required' };
    }
    if (address.trim().length < 5) {
      return { isValid: false, error: 'Address must be at least 5 characters' };
    }
    if (address.trim().length > 200) {
      return { isValid: false, error: 'Address must be less than 200 characters' };
    }
    return { isValid: true, error: '' };
  }

  // City, State, Zip validation
  static validateCityStateZip(cityStateZip) {
    if (!cityStateZip || cityStateZip.trim() === '') {
      return { isValid: false, error: 'City, State, Zip is required' };
    }
    
    // Expected format: "City, State, Zip" or "City, State Zip"
    const parts = cityStateZip.split(',');
    if (parts.length < 2) {
      return { isValid: false, error: 'Please use format: City, State, Zip' };
    }
    
    const city = parts[0].trim();
    const stateZip = parts[1].trim();
    
    if (city.length < 2) {
      return { isValid: false, error: 'City name must be at least 2 characters' };
    }
    
    if (stateZip.length < 4) {
      return { isValid: false, error: 'State and Zip must be provided' };
    }
    
    return { isValid: true, error: '' };
  }

  // Gender validation
  static validateGender(gender) {
    if (!gender || gender.trim() === '') {
      return { isValid: false, error: 'Gender is required' };
    }
    const validGenders = ['Male', 'Female', 'Other', 'Prefer not to say'];
    if (!validGenders.includes(gender)) {
      return { isValid: false, error: 'Please select a valid gender option' };
    }
    return { isValid: true, error: '' };
  }

  // Required field validation
  static validateRequired(value, fieldName) {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    return { isValid: true, error: '' };
  }

  // Dropdown selection validation
  static validateDropdownSelection(value, fieldName, validOptions = []) {
    if (!value || value === 'Select' || value.trim() === '') {
      return { isValid: false, error: `Please select ${fieldName}` };
    }
    if (validOptions.length > 0 && !validOptions.includes(value)) {
      return { isValid: false, error: `Please select a valid ${fieldName}` };
    }
    return { isValid: true, error: '' };
  }

  // Text area validation
  static validateTextArea(value, fieldName, minLength = 0, maxLength = 1000) {
    if (minLength > 0 && (!value || value.trim() === '')) {
      return { isValid: false, error: `${fieldName} is required` };
    }
    if (value && value.length > maxLength) {
      return { isValid: false, error: `${fieldName} must be less than ${maxLength} characters` };
    }
    if (value && value.trim().length < minLength) {
      return { isValid: false, error: `${fieldName} must be at least ${minLength} characters` };
    }
    return { isValid: true, error: '' };
  }

  // Checkbox group validation (at least one selected)
  static validateCheckboxGroup(selections, fieldName) {
    if (!selections || typeof selections !== 'object') {
      return { isValid: false, error: `Please select at least one ${fieldName}` };
    }
    
    const hasSelection = Object.values(selections).some(value => value === true);
    if (!hasSelection) {
      return { isValid: false, error: `Please select at least one ${fieldName}` };
    }
    
    return { isValid: true, error: '' };
  }

  // Score validation for assessments
  static validateScore(score, minScore = 0, maxScore = 100) {
    if (score === null || score === undefined || score === '') {
      return { isValid: false, error: 'Score is required' };
    }
    
    const scoreNum = parseInt(score);
    if (isNaN(scoreNum)) {
      return { isValid: false, error: 'Score must be a number' };
    }
    
    if (scoreNum < minScore || scoreNum > maxScore) {
      return { isValid: false, error: `Score must be between ${minScore} and ${maxScore}` };
    }
    
    return { isValid: true, error: '' };
  }

  // Assessment responses validation
  static validateAssessmentResponses(responses, totalQuestions) {
    if (!responses || Object.keys(responses).length === 0) {
      return {
        isValid: false,
        error: 'Please answer all questions before submitting.'
      };
    }

    for (let i = 0; i < totalQuestions; i++) {
      if (!responses[i]) {
        return {
          isValid: false,
          error: `Please answer question ${i + 1} before submitting.`
        };
      }
    }

    return { isValid: true };
  }

  // Medication selection validation
  static validateMedicationSelection(medications) {
    if (!medications || typeof medications !== 'object') {
      return { isValid: false, error: 'Please select at least one medication' };
    }
    
    const hasSelection = Object.values(medications).some(category => 
      Object.values(category).some(med => med === true)
    );
    
    if (!hasSelection) {
      return { isValid: false, error: 'Please select at least one medication' };
    }
    
    return { isValid: true, error: '' };
  }
}

// Form-specific validation functions
export const PatientIntakeValidation = {
  validateForm(formData) {
    const errors = {};
    
    // Full Legal Name
    const nameValidation = FormValidator.validateName(formData.fullLegalName, 'Full Legal Name');
    if (!nameValidation.isValid) errors.fullLegalName = nameValidation.error;
    
    // Phone
    const phoneValidation = FormValidator.validatePhone(formData.phone);
    if (!phoneValidation.isValid) errors.phone = phoneValidation.error;
    
    // Email
    const emailValidation = FormValidator.validateEmail(formData.email);
    if (!emailValidation.isValid) errors.email = emailValidation.error;
    
    // Address
    const addressValidation = FormValidator.validateAddress(formData.address);
    if (!addressValidation.isValid) errors.address = addressValidation.error;
    
    // City, State, Zip
    const cityStateZipValidation = FormValidator.validateCityStateZip(formData.cityStateZip);
    if (!cityStateZipValidation.isValid) errors.cityStateZip = cityStateZipValidation.error;
    
    // Age
    const ageValidation = FormValidator.validateAge(formData.age);
    if (!ageValidation.isValid) errors.age = ageValidation.error;
    
    // Date of Birth
    const dobValidation = FormValidator.validateDateOfBirth(formData.dob);
    if (!dobValidation.isValid) errors.dob = dobValidation.error;
    
    // Gender
    const genderValidation = FormValidator.validateGender(formData.gender);
    if (!genderValidation.isValid) errors.gender = genderValidation.error;
    
    // Active Duty Service Member
    const activeDutyValidation = FormValidator.validateDropdownSelection(
      formData.activeDutyServiceMember, 
      'Active Duty Service Member status',
      ['Yes', 'No']
    );
    if (!activeDutyValidation.isValid) errors.activeDutyServiceMember = activeDutyValidation.error;
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  validatePatientIntakeForm(formData) {
    const errors = {};
    const sections = {
      personal: 'Personal Information',
      military: 'Military Information',
      spouse: 'Spouse Information',
      healthcare: 'Healthcare Information',
      medical: 'Medical Information',
      emergency: 'Emergency Contact'
    };

    // Personal Information Section
    if (!formData.fullLegalName?.trim()) {
      errors.fullLegalName = `${sections.personal}: Full legal name is required`;
    }

    if (!formData.date) {
      errors.date = `${sections.personal}: Date is required`;
    }

    if (!formData.phone?.trim()) {
      errors.phone = `${sections.personal}: Phone number is required`;
    } else if (!/^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(formData.phone.replace(/\D/g, ''))) {
      errors.phone = `${sections.personal}: Please enter a valid phone number`;
    }

    if (!formData.email?.trim()) {
      errors.email = `${sections.personal}: Email is required`;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = `${sections.personal}: Please enter a valid email address`;
    }

    if (!formData.address?.trim()) {
      errors.address = `${sections.personal}: Address is required`;
    }

    if (!formData.cityStateZip?.trim()) {
      errors.cityStateZip = `${sections.personal}: City, State ZIP is required`;
    }

    if (!formData.age) {
      errors.age = `${sections.personal}: Age is required`;
    } else if (isNaN(parseInt(formData.age)) || parseInt(formData.age) <= 0) {
      errors.age = `${sections.personal}: Please enter a valid age`;
    }

    if (!formData.dob) {
      errors.dob = `${sections.personal}: Date of birth is required`;
    }

    if (!formData.gender) {
      errors.gender = `${sections.personal}: Gender is required`;
    }

    // Military Information Section
    if (!formData.activeDutyServiceMember) {
      errors.activeDutyServiceMember = `${sections.military}: Active duty service member status is required`;
    }

    // Emergency Contact Section
    if (!formData.emergencyContactName?.trim()) {
      errors.emergencyContactName = `${sections.emergency}: Emergency contact name is required`;
    }

    if (!formData.emergencyContactPhone?.trim()) {
      errors.emergencyContactPhone = `${sections.emergency}: Emergency contact phone is required`;
    } else if (!/^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(formData.emergencyContactPhone.replace(/\D/g, ''))) {
      errors.emergencyContactPhone = `${sections.emergency}: Please enter a valid phone number`;
    }

    if (!formData.emergencyContactRelationship?.trim()) {
      errors.emergencyContactRelationship = `${sections.emergency}: Emergency contact relationship is required`;
    }

    // Spouse Information Section (if spouse name is provided)
    if (formData.spouseName?.trim()) {
      if (!formData.spouseAge) {
        errors.spouseAge = `${sections.spouse}: Spouse age is required when spouse name is provided`;
      } else if (isNaN(parseInt(formData.spouseAge)) || parseInt(formData.spouseAge) <= 0) {
        errors.spouseAge = `${sections.spouse}: Please enter a valid spouse age`;
      }

      if (!formData.spouseDob) {
        errors.spouseDob = `${sections.spouse}: Spouse date of birth is required when spouse name is provided`;
      }
    }

    // Healthcare Information Section
    if (formData.primaryHealthInsurance?.trim()) {
      if (!formData.policy?.trim()) {
        errors.policy = `${sections.healthcare}: Policy number is required when insurance is provided`;
      }
      if (!formData.group?.trim()) {
        errors.group = `${sections.healthcare}: Group number is required when insurance is provided`;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  validateMedicalHistoryForm(formData) {
    const errors = {};
    const sections = {
      conditions: 'Medical Conditions',
      allergies: 'Allergies',
      medications: 'Current Medications',
      family: 'Family History'
    };

    // Validate at least one condition is selected or other condition is provided
    if (!formData.conditions?.length && !formData.otherConditions?.trim()) {
      errors.conditions = `${sections.conditions}: Please select at least one condition or specify other conditions`;
    }

    // Validate at least one allergy is selected or other allergy is provided
    if (!formData.allergies?.length && !formData.otherAllergies?.trim()) {
      errors.allergies = `${sections.allergies}: Please select at least one allergy or specify other allergies`;
    }

    // Validate at least one medication is selected or other medication is provided
    if (!formData.medications?.length && !formData.otherMedications?.trim()) {
      errors.medications = `${sections.medications}: Please select at least one medication or specify other medications`;
    }

    // Validate at least one family history item is selected or other family history is provided
    if (!formData.familyHistory?.length && !formData.otherFamilyHistory?.trim()) {
      errors.familyHistory = `${sections.family}: Please select at least one family history item or specify other family history`;
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  validatePreCertMedListForm(formData) {
    const errors = {};
    const sections = {
      medications: 'Medications',
      details: 'Medication Details'
    };

    // Validate at least one medication is selected
    if (!formData.medications?.length) {
      errors.medications = `${sections.medications}: Please select at least one medication`;
    }

    // Validate medication details for each selected medication
    if (formData.medications?.length) {
      formData.medications.forEach(medication => {
        if (!formData.medicationDetails?.[medication]?.dosage?.trim()) {
          errors[`${medication}_dosage`] = `${sections.details}: Dosage is required for ${medication}`;
        }
        if (!formData.medicationDetails?.[medication]?.frequency?.trim()) {
          errors[`${medication}_frequency`] = `${sections.details}: Frequency is required for ${medication}`;
        }
      });
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }
};
